import { Injectable, inject } from '@angular/core';
import { Observable, of, BehaviorSubject, combineLatest } from 'rxjs';
import { map, delay, switchMap } from 'rxjs/operators';
import {
  CustomReport,
  ReportConfiguration,
  ReportExecutionResult,
  ReportCategory,
  FilterOperator,
  ReportFilter,
  ReportDataSource,
  ReportVisualization,
  generateId
} from '../models/common.model';
import { UserService } from './user.service';
import { ProjectService } from './project.service';
import { BugService } from './bug.service';

@Injectable({
  providedIn: 'root'
})
export class CustomReportsService {
  private userService = inject(UserService);
  private projectService = inject(ProjectService);
  private bugService = inject(BugService);

  private reportsSubject = new BehaviorSubject<CustomReport[]>([]);
  public reports$ = this.reportsSubject.asObservable();

  private storageKey = 'custom_reports';

  constructor() {
    this.loadReportsFromStorage();
    this.initializeDefaultReports();
  }

  // CRUD Operations
  getAllReports(): Observable<CustomReport[]> {
    return this.reports$;
  }

  getReportById(id: string): Observable<CustomReport | null> {
    return this.reports$.pipe(
      map(reports => reports.find(r => r.id === id) || null)
    );
  }

  createReport(report: Omit<CustomReport, 'id' | 'createdAt' | 'updatedAt' | 'executionCount'>): Observable<CustomReport> {
    const newReport: CustomReport = {
      ...report,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      executionCount: 0
    };

    return of(newReport).pipe(
      delay(300),
      map(report => {
        const currentReports = this.reportsSubject.value;
        const updatedReports = [...currentReports, report];
        this.reportsSubject.next(updatedReports);
        this.saveReportsToStorage(updatedReports);
        return report;
      })
    );
  }

  updateReport(id: string, updates: Partial<CustomReport>): Observable<CustomReport> {
    return this.reports$.pipe(
      delay(300),
      map(reports => {
        const reportIndex = reports.findIndex(r => r.id === id);
        if (reportIndex === -1) {
          throw new Error('Report not found');
        }

        const updatedReport = {
          ...reports[reportIndex],
          ...updates,
          updatedAt: new Date()
        };

        const updatedReports = [...reports];
        updatedReports[reportIndex] = updatedReport;
        
        this.reportsSubject.next(updatedReports);
        this.saveReportsToStorage(updatedReports);
        
        return updatedReport;
      })
    );
  }

  deleteReport(id: string): Observable<void> {
    return this.reports$.pipe(
      delay(300),
      map(reports => {
        const updatedReports = reports.filter(r => r.id !== id);
        this.reportsSubject.next(updatedReports);
        this.saveReportsToStorage(updatedReports);
      })
    );
  }

  duplicateReport(id: string): Observable<CustomReport> {
    return this.getReportById(id).pipe(
      switchMap(report => {
        if (!report) {
          throw new Error('Report not found');
        }

        const duplicatedReport = {
          ...report,
          name: `${report.name} (Copy)`,
          createdBy: 'current-user', // Should be replaced with actual current user
          isPublic: false
        };

        delete (duplicatedReport as any).id;
        delete (duplicatedReport as any).createdAt;
        delete (duplicatedReport as any).updatedAt;
        delete (duplicatedReport as any).executionCount;

        return this.createReport(duplicatedReport);
      })
    );
  }

  // Report Execution
  executeReport(reportId: string): Observable<ReportExecutionResult> {
    return this.getReportById(reportId).pipe(
      delay(1000), // Simulate processing time
      switchMap(report => {
        if (!report) {
          throw new Error('Report not found');
        }

        // Update execution count
        this.updateReport(reportId, { 
          executionCount: report.executionCount + 1,
          lastExecuted: new Date()
        }).subscribe();

        return this.generateReportData(report);
      })
    );
  }

  private generateReportData(report: CustomReport): Observable<ReportExecutionResult> {
    return combineLatest([
      this.userService.getAllUsers(),
      this.projectService.projects$,
      this.bugService.bugs$
    ]).pipe(
      map(([users, projects, bugs]) => {
        const startTime = Date.now();
        let data: any[] = [];

        // Generate data based on report configuration
        switch (report.configuration.dataSource.type) {
          case 'bugs':
            data = this.generateBugReportData(bugs, report.configuration);
            break;
          case 'projects':
            data = this.generateProjectReportData(projects, report.configuration);
            break;
          case 'users':
            data = this.generateUserReportData(users, report.configuration);
            break;
          case 'team_performance':
            data = this.generateTeamPerformanceReportData(users, bugs, projects, report.configuration);
            break;
          default:
            data = this.generateCustomQueryData(report.configuration);
        }

        // Apply filters
        data = this.applyFilters(data, report.configuration.filters);

        // Apply sorting
        data = this.applySorting(data, report.configuration.sorting);

        const executionTime = Date.now() - startTime;

        return {
          reportId: report.id,
          executedAt: new Date(),
          executedBy: 'current-user', // Should be replaced with actual current user
          status: 'success' as const,
          data,
          metadata: {
            totalRows: data.length,
            executionTime,
            dataSource: report.configuration.dataSource.type,
            filters: report.configuration.filters
          }
        };
      })
    );
  }

  private generateBugReportData(bugs: any[], config: ReportConfiguration): any[] {
    return bugs.map(bug => ({
      id: bug.id,
      title: bug.title,
      status: bug.status,
      severity: bug.severity,
      priority: bug.priority,
      assignedTo: bug.assignedTo,
      createdAt: bug.createdAt,
      updatedAt: bug.updatedAt,
      projectId: bug.projectId
    }));
  }

  private generateProjectReportData(projects: any[], config: ReportConfiguration): any[] {
    return projects.map(project => ({
      id: project.id,
      name: project.name,
      status: project.status,
      teamMembersCount: project.teamMembers?.length || 0,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt
    }));
  }

  private generateUserReportData(users: any[], config: ReportConfiguration): any[] {
    return users.map(user => ({
      id: user.id,
      fullName: user.fullName,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt
    }));
  }

  private generateTeamPerformanceReportData(users: any[], bugs: any[], projects: any[], config: ReportConfiguration): any[] {
    return users.map(user => {
      const userBugs = bugs.filter(b => b.assignedTo === user.id);
      const resolvedBugs = userBugs.filter(b => b.status === 'CLOSED');
      
      return {
        userId: user.id,
        userName: user.fullName,
        role: user.role,
        bugsAssigned: userBugs.length,
        bugsResolved: resolvedBugs.length,
        fixRate: userBugs.length > 0 ? (resolvedBugs.length / userBugs.length) * 100 : 0,
        projectsCount: projects.filter(p => p.teamMembers?.some((tm: any) => tm.userId === user.id)).length
      };
    });
  }

  private generateCustomQueryData(config: ReportConfiguration): any[] {
    // Mock data for custom queries
    return [
      { id: 1, value: 'Custom Data 1', count: 10 },
      { id: 2, value: 'Custom Data 2', count: 15 },
      { id: 3, value: 'Custom Data 3', count: 8 }
    ];
  }

  private applyFilters(data: any[], filters: ReportFilter[]): any[] {
    return data.filter(item => {
      return filters.every(filter => {
        const value = item[filter.field];
        return this.evaluateFilter(value, filter);
      });
    });
  }

  private evaluateFilter(value: any, filter: ReportFilter): boolean {
    switch (filter.operator) {
      case FilterOperator.EQUALS:
        return value === filter.value;
      case FilterOperator.NOT_EQUALS:
        return value !== filter.value;
      case FilterOperator.CONTAINS:
        return String(value).toLowerCase().includes(String(filter.value).toLowerCase());
      case FilterOperator.NOT_CONTAINS:
        return !String(value).toLowerCase().includes(String(filter.value).toLowerCase());
      case FilterOperator.GREATER_THAN:
        return Number(value) > Number(filter.value);
      case FilterOperator.LESS_THAN:
        return Number(value) < Number(filter.value);
      case FilterOperator.GREATER_EQUAL:
        return Number(value) >= Number(filter.value);
      case FilterOperator.LESS_EQUAL:
        return Number(value) <= Number(filter.value);
      case FilterOperator.IN:
        return Array.isArray(filter.value) && filter.value.includes(value);
      case FilterOperator.NOT_IN:
        return Array.isArray(filter.value) && !filter.value.includes(value);
      case FilterOperator.IS_NULL:
        return value == null;
      case FilterOperator.IS_NOT_NULL:
        return value != null;
      default:
        return true;
    }
  }

  private applySorting(data: any[], sorting: any[]): any[] {
    if (!sorting || sorting.length === 0) return data;

    return data.sort((a, b) => {
      for (const sort of sorting) {
        const aValue = a[sort.field];
        const bValue = b[sort.field];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        else if (aValue > bValue) comparison = 1;
        
        if (comparison !== 0) {
          return sort.direction === 'DESC' ? -comparison : comparison;
        }
      }
      return 0;
    });
  }

  // Report Templates
  getReportTemplates(): Observable<CustomReport[]> {
    return of(this.getDefaultReportTemplates()).pipe(delay(300));
  }

  private getDefaultReportTemplates(): CustomReport[] {
    return [
      {
        id: 'template-bug-summary',
        name: 'Bug Summary Report',
        description: 'Comprehensive overview of all bugs with status and priority breakdown',
        createdBy: 'system',
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublic: true,
        category: ReportCategory.BUG_ANALYSIS,
        configuration: {
          dataSource: { type: 'bugs', tables: ['bugs'] },
          filters: [],
          groupBy: ['status', 'priority'],
          aggregations: [{ field: 'id', function: 'COUNT', alias: 'total_bugs' }],
          visualization: {
            type: 'chart',
            chartType: 'bar',
            layout: 'single',
            options: { showLegend: true, showGrid: true, colors: [], height: 400, responsive: true }
          },
          columns: [
            { field: 'title', label: 'Title', dataType: 'string', sortable: true, filterable: true, visible: true },
            { field: 'status', label: 'Status', dataType: 'string', sortable: true, filterable: true, visible: true },
            { field: 'priority', label: 'Priority', dataType: 'string', sortable: true, filterable: true, visible: true }
          ],
          sorting: [{ field: 'createdAt', direction: 'DESC', priority: 1 }],
          dateRange: { type: 'relative', relativePeriod: 'last_30_days' }
        },
        executionCount: 0,
        tags: ['bugs', 'summary', 'status']
      }
    ];
  }

  // Storage methods
  private loadReportsFromStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem(this.storageKey);
        if (stored) {
          const reports = JSON.parse(stored);
          this.reportsSubject.next(reports);
        }
      }
    } catch (error) {
      console.error('Error loading reports from storage:', error);
    }
  }

  private saveReportsToStorage(reports: CustomReport[]): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(this.storageKey, JSON.stringify(reports));
      }
    } catch (error) {
      console.error('Error saving reports to storage:', error);
    }
  }

  private initializeDefaultReports(): void {
    const currentReports = this.reportsSubject.value;
    if (currentReports.length === 0) {
      // Initialize with some default reports
      const defaultReports = this.getDefaultReportTemplates();
      this.reportsSubject.next(defaultReports);
      this.saveReportsToStorage(defaultReports);
    }
  }
}
