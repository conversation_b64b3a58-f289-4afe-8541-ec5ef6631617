import { Injectable, inject } from '@angular/core';
import { Observable, of, combineLatest } from 'rxjs';
import { map, delay } from 'rxjs/operators';
import { 
  TeamPerformanceMetrics, 
  TeamOverviewMetrics, 
  IndividualPerformanceMetrics,
  TeamProductivityMetrics,
  WorkloadDistributionMetrics,
  PerformanceTrendData,
  TeamComparisonMetrics,
  MemberWorkload,
  WorkloadRecommendation,
  TeamPeriodMetrics
} from '../models/common.model';
import { UserService } from './user.service';
import { ProjectService } from './project.service';
import { BugService } from './bug.service';
import { User, UserRole } from '../models/user.model';
import { BugStatus, BugSeverity } from '../models/bug.model';

@Injectable({
  providedIn: 'root'
})
export class TeamPerformanceService {
  private userService = inject(UserService);
  private projectService = inject(ProjectService);
  private bugService = inject(BugService);

  getTeamPerformanceMetrics(dateRange?: { start: Date; end: Date }): Observable<TeamPerformanceMetrics> {
    return combineLatest([
      this.userService.getAllUsers(),
      this.projectService.projects$,
      this.bugService.bugs$
    ]).pipe(
      delay(800), // Simulate API call
      map(([users, projects, bugs]) => {
        const teamOverview = this.generateTeamOverview(users, projects, bugs);
        const individualPerformance = this.generateIndividualPerformance(users, bugs);
        const teamProductivity = this.generateTeamProductivity(users, bugs, projects);
        const workloadDistribution = this.generateWorkloadDistribution(users, bugs);
        const performanceTrends = this.generatePerformanceTrends();
        const teamComparison = this.generateTeamComparison();

        return {
          teamOverview,
          individualPerformance,
          teamProductivity,
          workloadDistribution,
          performanceTrends,
          teamComparison
        };
      })
    );
  }

  private generateTeamOverview(users: User[], projects: any[], bugs: any[]): TeamOverviewMetrics {
    const activeMembers = users.filter(u => u.isActive).length;
    const totalBugsResolved = bugs.filter(b => b.status === BugStatus.CLOSED).length;
    const activeProjects = projects.filter(p => p.status === 'ACTIVE').length;
    
    return {
      totalMembers: users.length,
      activeMembers,
      averageProductivity: Math.floor(Math.random() * 20) + 75, // 75-95%
      totalBugsResolved,
      totalProjectsActive: activeProjects,
      teamEfficiencyScore: Math.floor(Math.random() * 15) + 80, // 80-95%
      averageResolutionTime: Math.floor(Math.random() * 10) + 15, // 15-25 hours
      teamBurnoutRisk: this.calculateBurnoutRisk()
    };
  }

  private generateIndividualPerformance(users: User[], bugs: any[]): IndividualPerformanceMetrics[] {
    return users.slice(0, 8).map(user => {
      const userBugs = bugs.filter(b => b.assignedTo === user.id);
      const resolvedBugs = userBugs.filter(b => b.status === BugStatus.CLOSED);
      const inProgressBugs = userBugs.filter(b => b.status === BugStatus.IN_PROGRESS);
      const pendingBugs = userBugs.filter(b => [BugStatus.NEW, BugStatus.IN_PROGRESS].includes(b.status));

      const fixRate = userBugs.length > 0 ? (resolvedBugs.length / userBugs.length) * 100 : 0;
      const productivityScore = Math.floor(Math.random() * 30) + 70;
      const qualityScore = Math.floor(Math.random() * 25) + 75;
      const workloadScore = Math.floor(Math.random() * 40) + 60;

      return {
        userId: user.id,
        userName: user.fullName,
        userRole: user.role,
        avatar: user.avatar,
        performance: {
          bugsAssigned: userBugs.length,
          bugsResolved: resolvedBugs.length,
          bugsInProgress: inProgressBugs.length,
          bugsPending: pendingBugs.length,
          averageResolutionTime: Math.floor(Math.random() * 20) + 10,
          fixRate: Math.round(fixRate),
          reopenRate: Math.floor(Math.random() * 10) + 2,
          productivityScore,
          qualityScore,
          workloadScore
        },
        projectsInvolved: Math.floor(Math.random() * 4) + 1,
        lastActiveDate: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        performanceRating: this.getPerformanceRating(productivityScore, qualityScore),
        strengths: this.generateStrengths(user.role),
        improvementAreas: this.generateImprovementAreas()
      };
    });
  }

  private generateTeamProductivity(users: User[], bugs: any[], projects: any[]): TeamProductivityMetrics {
    const completedTasks = bugs.filter(b => b.status === BugStatus.CLOSED).length;
    const activeMembers = users.filter(u => u.isActive).length;
    
    return {
      totalTasksCompleted: completedTasks,
      averageTasksPerMember: activeMembers > 0 ? Math.round(completedTasks / activeMembers) : 0,
      productivityTrend: ['increasing', 'stable', 'decreasing'][Math.floor(Math.random() * 3)] as any,
      sprintVelocity: Math.floor(Math.random() * 20) + 25,
      teamCapacity: activeMembers * 40, // 40 hours per week per member
      utilizationRate: Math.floor(Math.random() * 20) + 75,
      collaborationScore: Math.floor(Math.random() * 15) + 80,
      codeReviewMetrics: {
        averageReviewTime: Math.floor(Math.random() * 8) + 4,
        reviewParticipation: Math.floor(Math.random() * 20) + 75,
        qualityScore: Math.floor(Math.random() * 15) + 80
      }
    };
  }

  private generateWorkloadDistribution(users: User[], bugs: any[]): WorkloadDistributionMetrics {
    const memberWorkloads: MemberWorkload[] = users.slice(0, 8).map(user => {
      const userBugs = bugs.filter(b => b.assignedTo === user.id && b.status !== BugStatus.CLOSED);
      const capacity = 40; // 40 hours per week
      const currentWorkload = userBugs.length * 4; // 4 hours per bug estimate
      const utilizationPercentage = Math.round((currentWorkload / capacity) * 100);
      
      return {
        userId: user.id,
        userName: user.fullName,
        currentWorkload,
        capacity,
        utilizationPercentage,
        upcomingDeadlines: Math.floor(Math.random() * 5),
        workloadStatus: this.getWorkloadStatus(utilizationPercentage),
        taskBreakdown: {
          bugs: userBugs.length,
          features: Math.floor(Math.random() * 3),
          reviews: Math.floor(Math.random() * 5),
          meetings: Math.floor(Math.random() * 8) + 2
        }
      };
    });

    const overloadedMembers = memberWorkloads
      .filter(m => m.workloadStatus === 'overloaded')
      .map(m => m.userName);
    
    const underutilizedMembers = memberWorkloads
      .filter(m => m.workloadStatus === 'underutilized')
      .map(m => m.userName);

    return {
      memberWorkloads,
      workloadBalance: this.calculateWorkloadBalance(memberWorkloads),
      overloadedMembers,
      underutilizedMembers,
      recommendedRebalancing: this.generateRebalancingRecommendations(memberWorkloads)
    };
  }

  private generatePerformanceTrends(): PerformanceTrendData[] {
    const trends: PerformanceTrendData[] = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    for (let i = 0; i < 30; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      trends.push({
        date,
        teamProductivity: Math.floor(Math.random() * 20) + 75,
        bugsResolved: Math.floor(Math.random() * 10) + 5,
        newBugsReported: Math.floor(Math.random() * 8) + 3,
        teamMorale: Math.floor(Math.random() * 15) + 80,
        averageResolutionTime: Math.floor(Math.random() * 10) + 15,
        qualityScore: Math.floor(Math.random() * 20) + 75,
        memberCount: 8
      });
    }

    return trends;
  }

  private generateTeamComparison(): TeamComparisonMetrics {
    const currentPeriod: TeamPeriodMetrics = {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      bugsResolved: Math.floor(Math.random() * 50) + 100,
      averageResolutionTime: Math.floor(Math.random() * 10) + 15,
      productivityScore: Math.floor(Math.random() * 20) + 75,
      qualityScore: Math.floor(Math.random() * 15) + 80,
      teamSatisfaction: Math.floor(Math.random() * 20) + 75,
      projectsCompleted: Math.floor(Math.random() * 3) + 2
    };

    const previousPeriod: TeamPeriodMetrics = {
      startDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      bugsResolved: Math.floor(Math.random() * 40) + 80,
      averageResolutionTime: Math.floor(Math.random() * 15) + 18,
      productivityScore: Math.floor(Math.random() * 15) + 70,
      qualityScore: Math.floor(Math.random() * 10) + 75,
      teamSatisfaction: Math.floor(Math.random() * 15) + 70,
      projectsCompleted: Math.floor(Math.random() * 2) + 1
    };

    return {
      currentPeriod,
      previousPeriod,
      percentageChanges: {
        productivity: this.calculatePercentageChange(currentPeriod.productivityScore, previousPeriod.productivityScore),
        quality: this.calculatePercentageChange(currentPeriod.qualityScore, previousPeriod.qualityScore),
        efficiency: this.calculatePercentageChange(previousPeriod.averageResolutionTime, currentPeriod.averageResolutionTime),
        satisfaction: this.calculatePercentageChange(currentPeriod.teamSatisfaction, previousPeriod.teamSatisfaction)
      },
      benchmarkComparison: {
        industryAverage: 78,
        companyAverage: 82,
        teamRanking: Math.floor(Math.random() * 5) + 1
      }
    };
  }

  // Helper methods
  private calculateBurnoutRisk(): 'low' | 'medium' | 'high' {
    const risk = Math.random();
    if (risk < 0.7) return 'low';
    if (risk < 0.9) return 'medium';
    return 'high';
  }

  private getPerformanceRating(productivity: number, quality: number): 'excellent' | 'good' | 'average' | 'needs_improvement' {
    const average = (productivity + quality) / 2;
    if (average >= 90) return 'excellent';
    if (average >= 80) return 'good';
    if (average >= 70) return 'average';
    return 'needs_improvement';
  }

  private generateStrengths(role: UserRole): string[] {
    const strengthsMap: { [key in UserRole]: string[] } = {
      [UserRole.ADMIN]: ['Leadership', 'Strategic Planning', 'Team Management'],
      [UserRole.PROJECT_MANAGER]: ['Project Coordination', 'Risk Management', 'Stakeholder Communication'],
      [UserRole.DEVELOPER]: ['Code Quality', 'Problem Solving', 'Technical Innovation'],
      [UserRole.QA_ENGINEER]: ['Attention to Detail', 'Test Coverage', 'Bug Detection'],
      [UserRole.QA_LEAD]: ['Quality Assurance', 'Process Improvement', 'Team Mentoring'],
      [UserRole.DEV_LEAD]: ['Technical Leadership', 'Code Review', 'Architecture Design'],
      [UserRole.BUSINESS_ANALYST]: ['Requirements Analysis', 'Documentation', 'Stakeholder Liaison']
    };
    
    const roleStrengths = strengthsMap[role] || ['Collaboration', 'Communication', 'Reliability'];
    return roleStrengths.slice(0, Math.floor(Math.random() * 2) + 2);
  }

  private generateImprovementAreas(): string[] {
    const areas = [
      'Time Management', 'Communication', 'Technical Skills', 'Documentation',
      'Code Review Participation', 'Meeting Efficiency', 'Knowledge Sharing'
    ];
    return areas.slice(0, Math.floor(Math.random() * 2) + 1);
  }

  private getWorkloadStatus(utilization: number): 'overloaded' | 'optimal' | 'underutilized' {
    if (utilization > 90) return 'overloaded';
    if (utilization < 60) return 'underutilized';
    return 'optimal';
  }

  private calculateWorkloadBalance(workloads: MemberWorkload[]): 'balanced' | 'unbalanced' | 'critical' {
    const utilizationRates = workloads.map(w => w.utilizationPercentage);
    const max = Math.max(...utilizationRates);
    const min = Math.min(...utilizationRates);
    const difference = max - min;
    
    if (difference > 50) return 'critical';
    if (difference > 30) return 'unbalanced';
    return 'balanced';
  }

  private generateRebalancingRecommendations(workloads: MemberWorkload[]): WorkloadRecommendation[] {
    const overloaded = workloads.filter(w => w.workloadStatus === 'overloaded');
    const underutilized = workloads.filter(w => w.workloadStatus === 'underutilized');
    
    const recommendations: WorkloadRecommendation[] = [];
    
    overloaded.forEach(member => {
      if (underutilized.length > 0) {
        const target = underutilized[Math.floor(Math.random() * underutilized.length)];
        recommendations.push({
          type: 'redistribute',
          fromUserId: member.userId,
          toUserId: target.userId,
          taskCount: Math.floor(Math.random() * 3) + 1,
          priority: 'high',
          reason: `Redistribute tasks from overloaded ${member.userName} to underutilized ${target.userName}`
        });
      }
    });
    
    return recommendations;
  }

  private calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return 0;
    return Math.round(((current - previous) / previous) * 100);
  }
}
