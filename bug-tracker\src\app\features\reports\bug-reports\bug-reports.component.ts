import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { BugService } from '../../../core/services/bug.service';
import { ProjectService } from '../../../core/services/project.service';
import { UserService } from '../../../core/services/user.service';
import { BugStatus, BugSeverity, BugPriority, BugMetrics } from '../../../core/models/bug.model';
import { Project } from '../../../core/models/project.model';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-bug-reports',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="reports-container">
      <div class="reports-header">
        <h1>Bug Reports & Analytics</h1>
        <p>Comprehensive bug tracking and analysis dashboard</p>
      </div>

      <!-- Filters Section -->
      <div class="filters-section">
        <form [formGroup]="filterForm" class="filters-form">
          <div class="filter-row">
            <div class="filter-group">
              <label>Date Range</label>
              <div class="date-range">
                <input type="date" formControlName="startDate" class="form-control">
                <span>to</span>
                <input type="date" formControlName="endDate" class="form-control">
              </div>
            </div>

            <div class="filter-group">
              <label>Status</label>
              <select formControlName="status" class="form-control">
                <option value="">All Statuses</option>
                <option value="NEW">New</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="RESOLVED">Resolved</option>
                <option value="CLOSED">Closed</option>
                <option value="REOPENED">Reopened</option>
              </select>
            </div>

            <div class="filter-group">
              <label>Project</label>
              <select formControlName="projectId" class="form-control">
                <option value="">All Projects</option>
                <option *ngFor="let project of projects" [value]="project.id">
                  {{ project.name }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>Assignee</label>
              <select formControlName="assigneeId" class="form-control">
                <option value="">All Assignees</option>
                <option *ngFor="let user of users" [value]="user.id">
                  {{ user.fullName }}
                </option>
              </select>
            </div>
          </div>

          <div class="filter-actions">
            <button type="button" (click)="applyFilters()" class="btn btn-primary">
              Apply Filters
            </button>
            <button type="button" (click)="resetFilters()" class="btn btn-outline">
              Reset
            </button>
            <button type="button" (click)="exportReport()" class="btn btn-success">
              Export Report
            </button>
          </div>
        </form>
      </div>

      <!-- Summary Cards -->
      <div class="summary-cards">
        <div class="summary-card total-bugs">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 8V10C19 11.1 18.1 12 17 12H15L13.5 7.5C13.1 6.6 12.2 6 11.2 6H10.8C9.8 6 8.9 6.6 8.5 7.5L7 12H5C3.9 12 3 11.1 3 10V8L1 7V9C1 10.7 2.3 12 4 12H6V22H8V16H10V22H12V16H14V22H16V12H18C19.7 12 21 10.7 21 9Z"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.totalBugs || 0 }}</h3>
            <p>Total Bugs</p>
          </div>
        </div>

        <div class="summary-card open-bugs">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM12 20C10.9 20 10 19.1 10 18C10 16.9 10.9 16 12 16C13.1 16 14 16.9 14 18C14 19.1 13.1 20 12 20ZM12 7C13.1 7 14 7.9 14 9V15C14 16.1 13.1 17 12 17C10.9 17 10 16.1 10 15V9C10 7.9 10.9 7 12 7Z"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.openBugs || 0 }}</h3>
            <p>Open Bugs</p>
          </div>
        </div>

        <div class="summary-card closed-bugs">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM9 12L11 14L15 10L13.6 8.6L11 11.2L10.4 10.6L9 12ZM12 20C10.9 20 10 19.1 10 18C10 16.9 10.9 16 12 16C13.1 16 14 16.9 14 18C14 19.1 13.1 20 12 20Z"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.closedBugs || 0 }}</h3>
            <p>Closed Bugs</p>
          </div>
        </div>

        <div class="summary-card critical-bugs">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2L13.09 8.26L22 9L17.5 13.74L18.18 22L12 19.77L5.82 22L6.5 13.74L2 9L10.91 8.26L12 2Z"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.criticalBugs || 0 }}</h3>
            <p>Critical Bugs</p>
          </div>
        </div>

        <div class="summary-card avg-time">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M12 20C7.6 20 4 16.4 4 12S7.6 4 12 4 20 7.6 20 12 16.4 20 12 20M12.5 7V12.25L17 14.92L16.25 16.15L11 13V7H12.5Z"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.averageTimeToFix || 0 }}d</h3>
            <p>Avg Resolution Time</p>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <div class="charts-grid">
          <!-- Bug Status Distribution -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Bug Status Distribution</h3>
              <p>Current distribution of bugs by status</p>
            </div>
            <div class="chart-content">
              <div class="pie-chart">
                <div class="chart-legend">
                  <div class="legend-item">
                    <div class="legend-color new"></div>
                    <span>New ({{ getStatusCount('NEW') }})</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color progress"></div>
                    <span>In Progress ({{ getStatusCount('IN_PROGRESS') }})</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color fixed"></div>
                    <span>Fixed ({{ getStatusCount('FIXED') }})</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color closed"></div>
                    <span>Closed ({{ getStatusCount('CLOSED') }})</span>
                  </div>
                </div>
                <div class="pie-visual">
                  <svg width="200" height="200" viewBox="0 0 200 200">
                    <circle cx="100" cy="100" r="80" fill="none" stroke="#e5e7eb" stroke-width="20"></circle>
                    <circle cx="100" cy="100" r="80" fill="none" stroke="#ef4444" stroke-width="20"
                            [attr.stroke-dasharray]="getCircumference()"
                            [attr.stroke-dashoffset]="getStatusOffset('NEW')"
                            transform="rotate(-90 100 100)"></circle>
                    <circle cx="100" cy="100" r="80" fill="none" stroke="#f59e0b" stroke-width="20"
                            [attr.stroke-dasharray]="getCircumference()"
                            [attr.stroke-dashoffset]="getStatusOffset('IN_PROGRESS')"
                            transform="rotate(-90 100 100)"></circle>
                    <circle cx="100" cy="100" r="80" fill="none" stroke="#10b981" stroke-width="20"
                            [attr.stroke-dasharray]="getCircumference()"
                            [attr.stroke-dashoffset]="getStatusOffset('FIXED')"
                            transform="rotate(-90 100 100)"></circle>
                    <circle cx="100" cy="100" r="80" fill="none" stroke="#3b82f6" stroke-width="20"
                            [attr.stroke-dasharray]="getCircumference()"
                            [attr.stroke-dashoffset]="getStatusOffset('CLOSED')"
                            transform="rotate(-90 100 100)"></circle>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Bug Severity Distribution -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Severity Distribution</h3>
              <p>Bugs categorized by severity level</p>
            </div>
            <div class="chart-content">
              <div class="bar-chart">
                <div class="bar-item">
                  <div class="bar-label">Critical</div>
                  <div class="bar-container">
                    <div class="bar critical" [style.width.%]="getSeverityPercentage('CRITICAL')"></div>
                  </div>
                  <div class="bar-value">{{ getSeverityCount('CRITICAL') }}</div>
                </div>
                <div class="bar-item">
                  <div class="bar-label">High</div>
                  <div class="bar-container">
                    <div class="bar high" [style.width.%]="getSeverityPercentage('HIGH')"></div>
                  </div>
                  <div class="bar-value">{{ getSeverityCount('HIGH') }}</div>
                </div>
                <div class="bar-item">
                  <div class="bar-label">Medium</div>
                  <div class="bar-container">
                    <div class="bar medium" [style.width.%]="getSeverityPercentage('MEDIUM')"></div>
                  </div>
                  <div class="bar-value">{{ getSeverityCount('MEDIUM') }}</div>
                </div>
                <div class="bar-item">
                  <div class="bar-label">Low</div>
                  <div class="bar-container">
                    <div class="bar low" [style.width.%]="getSeverityPercentage('LOW')"></div>
                  </div>
                  <div class="bar-value">{{ getSeverityCount('LOW') }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Bug Trends Over Time -->
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3>Bug Trends Over Time</h3>
              <p>Daily bug creation and resolution trends</p>
            </div>
            <div class="chart-content">
              <div class="line-chart">
                <svg width="100%" height="300" viewBox="0 0 800 300">
                  <!-- Grid lines -->
                  <defs>
                    <pattern id="grid" width="80" height="30" patternUnits="userSpaceOnUse">
                      <path d="M 80 0 L 0 0 0 30" fill="none" stroke="#f3f4f6" stroke-width="1"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />

                  <!-- Sample trend lines -->
                  <polyline points="50,250 130,200 210,180 290,220 370,160 450,140 530,120 610,100 690,80 770,60"
                            fill="none" stroke="#ef4444" stroke-width="3" opacity="0.8"/>
                  <polyline points="50,200 130,180 210,160 290,140 370,120 450,100 530,90 610,70 690,50 770,40"
                            fill="none" stroke="#10b981" stroke-width="3" opacity="0.8"/>

                  <!-- Data points -->
                  <circle cx="50" cy="250" r="4" fill="#ef4444"/>
                  <circle cx="130" cy="200" r="4" fill="#ef4444"/>
                  <circle cx="210" cy="180" r="4" fill="#ef4444"/>
                  <circle cx="290" cy="220" r="4" fill="#ef4444"/>
                  <circle cx="370" cy="160" r="4" fill="#ef4444"/>
                  <circle cx="450" cy="140" r="4" fill="#ef4444"/>
                  <circle cx="530" cy="120" r="4" fill="#ef4444"/>
                  <circle cx="610" cy="100" r="4" fill="#ef4444"/>
                  <circle cx="690" cy="80" r="4" fill="#ef4444"/>
                  <circle cx="770" cy="60" r="4" fill="#ef4444"/>

                  <circle cx="50" cy="200" r="4" fill="#10b981"/>
                  <circle cx="130" cy="180" r="4" fill="#10b981"/>
                  <circle cx="210" cy="160" r="4" fill="#10b981"/>
                  <circle cx="290" cy="140" r="4" fill="#10b981"/>
                  <circle cx="370" cy="120" r="4" fill="#10b981"/>
                  <circle cx="450" cy="100" r="4" fill="#10b981"/>
                  <circle cx="530" cy="90" r="4" fill="#10b981"/>
                  <circle cx="610" cy="70" r="4" fill="#10b981"/>
                  <circle cx="690" cy="50" r="4" fill="#10b981"/>
                  <circle cx="770" cy="40" r="4" fill="#10b981"/>
                </svg>
                <div class="chart-legend horizontal">
                  <div class="legend-item">
                    <div class="legend-color" style="background: #ef4444;"></div>
                    <span>Bugs Reported</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color" style="background: #10b981;"></div>
                    <span>Bugs Resolved</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Priority vs Severity Matrix -->
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3>Priority vs Severity Matrix</h3>
              <p>Bug distribution across priority and severity levels</p>
            </div>
            <div class="chart-content">
              <div class="matrix-chart">
                <div class="matrix-grid">
                  <div class="matrix-header">
                    <div class="matrix-cell"></div>
                    <div class="matrix-cell header">Low Priority</div>
                    <div class="matrix-cell header">Medium Priority</div>
                    <div class="matrix-cell header">High Priority</div>
                    <div class="matrix-cell header">Critical Priority</div>
                  </div>
                  <div class="matrix-row">
                    <div class="matrix-cell header">Critical Severity</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(1, 4)">{{ getMatrixValue('CRITICAL', 'LOW') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(2, 4)">{{ getMatrixValue('CRITICAL', 'MEDIUM') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(3, 4)">{{ getMatrixValue('CRITICAL', 'HIGH') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(4, 4)">{{ getMatrixValue('CRITICAL', 'CRITICAL') }}</div>
                  </div>
                  <div class="matrix-row">
                    <div class="matrix-cell header">High Severity</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(1, 3)">{{ getMatrixValue('HIGH', 'LOW') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(2, 3)">{{ getMatrixValue('HIGH', 'MEDIUM') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(3, 3)">{{ getMatrixValue('HIGH', 'HIGH') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(4, 3)">{{ getMatrixValue('HIGH', 'CRITICAL') }}</div>
                  </div>
                  <div class="matrix-row">
                    <div class="matrix-cell header">Medium Severity</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(1, 2)">{{ getMatrixValue('MEDIUM', 'LOW') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(2, 2)">{{ getMatrixValue('MEDIUM', 'MEDIUM') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(3, 2)">{{ getMatrixValue('MEDIUM', 'HIGH') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(4, 2)">{{ getMatrixValue('MEDIUM', 'CRITICAL') }}</div>
                  </div>
                  <div class="matrix-row">
                    <div class="matrix-cell header">Low Severity</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(1, 1)">{{ getMatrixValue('LOW', 'LOW') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(2, 1)">{{ getMatrixValue('LOW', 'MEDIUM') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(3, 1)">{{ getMatrixValue('LOW', 'HIGH') }}</div>
                    <div class="matrix-cell data" [style.background-color]="getMatrixColor(4, 1)">{{ getMatrixValue('LOW', 'CRITICAL') }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .reports-container {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .reports-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .reports-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 0.5rem;
    }

    .reports-header p {
      font-size: 1.1rem;
      color: #6b7280;
    }

    .filters-section {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      position: relative;
      z-index: 1;
    }

    .filter-group label {
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }

    .form-control {
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.875rem;
      position: relative;
      z-index: 2;
    }

    .form-control:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .date-range {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .date-range span {
      color: #6b7280;
      font-size: 0.875rem;
      white-space: nowrap;
    }

    .filter-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      flex-wrap: wrap;
      margin-top: 1rem;
    }

    @media (max-width: 768px) {
      .filter-row {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .date-range {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
      }

      .filter-actions {
        justify-content: stretch;
      }

      .filter-actions .btn {
        flex: 1;
      }
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
    }

    .btn-outline {
      background: transparent;
      color: #6b7280;
      border: 1px solid #d1d5db;
    }

    .btn-success {
      background: #10b981;
      color: white;
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.5rem;
    }

    .total-bugs .card-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .open-bugs .card-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .closed-bugs .card-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .critical-bugs .card-icon {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .avg-time .card-icon {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      color: #374151;
    }

    .card-content h3 {
      font-size: 2rem;
      font-weight: 700;
      color: #1f2937;
      margin: 0;
    }

    .card-content p {
      color: #6b7280;
      margin: 0;
      font-size: 0.875rem;
    }

    .charts-section {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .charts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .chart-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
    }

    .chart-card.full-width {
      grid-column: 1 / -1;
    }

    .chart-header {
      margin-bottom: 1.5rem;
      border-bottom: 1px solid #e5e7eb;
      padding-bottom: 1rem;
    }

    .chart-header h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #111827;
      margin: 0 0 0.5rem 0;
    }

    .chart-header p {
      color: #6b7280;
      font-size: 0.875rem;
      margin: 0;
    }

    .chart-content {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .pie-chart {
      display: flex;
      align-items: center;
      gap: 2rem;
      width: 100%;
    }

    .chart-legend {
      flex: 1;
    }

    .chart-legend.horizontal {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 1rem;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }

    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 4px;
    }

    .legend-color.new { background: #ef4444; }
    .legend-color.progress { background: #f59e0b; }
    .legend-color.fixed { background: #10b981; }
    .legend-color.closed { background: #3b82f6; }

    .pie-visual {
      flex-shrink: 0;
    }

    .bar-chart {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .bar-item {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .bar-label {
      width: 80px;
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
    }

    .bar-container {
      flex: 1;
      height: 24px;
      background: #f3f4f6;
      border-radius: 12px;
      overflow: hidden;
    }

    .bar {
      height: 100%;
      border-radius: 12px;
      transition: width 0.3s ease;
    }

    .bar.critical { background: #ef4444; }
    .bar.high { background: #f59e0b; }
    .bar.medium { background: #10b981; }
    .bar.low { background: #3b82f6; }

    .bar-value {
      width: 40px;
      text-align: right;
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
    }

    .line-chart {
      width: 100%;
      height: 100%;
      position: relative;
    }

    .matrix-chart {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .matrix-grid {
      display: grid;
      grid-template-columns: 120px repeat(4, 1fr);
      gap: 1px;
      background: #e5e7eb;
      border-radius: 8px;
      overflow: hidden;
    }

    .matrix-cell {
      background: white;
      padding: 0.75rem;
      text-align: center;
      font-size: 0.875rem;
    }

    .matrix-cell.header {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
    }

    .matrix-cell.data {
      font-weight: 600;
      color: #111827;
      min-height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    @media (max-width: 768px) {
      .charts-grid {
        grid-template-columns: 1fr;
      }

      .pie-chart {
        flex-direction: column;
        gap: 1rem;
      }

      .chart-legend.horizontal {
        flex-direction: column;
        gap: 0.5rem;
      }

      .matrix-grid {
        grid-template-columns: 80px repeat(4, 1fr);
        font-size: 0.75rem;
      }

      .matrix-cell {
        padding: 0.5rem;
      }
    }
  `]
})
export class BugReportsComponent implements OnInit {
  private fb = inject(FormBuilder);
  private bugService = inject(BugService);
  private projectService = inject(ProjectService);
  private userService = inject(UserService);

  filterForm!: FormGroup;
  projects: Project[] = [];
  users: User[] = [];
  bugMetrics: BugMetrics | null = null;
  loading = false;

  ngOnInit() {
    this.initializeForm();
    this.loadData();
  }

  private initializeForm() {
    this.filterForm = this.fb.group({
      startDate: [''],
      endDate: [''],
      status: [''],
      projectId: [''],
      assigneeId: ['']
    });
  }

  private loadData() {
    this.loading = true;

    // Load projects
    this.projectService.projects$.subscribe({
      next: (projects) => {
        this.projects = projects;
      },
      error: (error) => {
        console.error('Error loading projects:', error);
      }
    });

    // Load users
    this.userService.getAllUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
      }
    });

    // Load bug metrics
    this.bugService.getBugMetrics().subscribe({
      next: (metrics) => {
        this.bugMetrics = metrics;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading bug metrics:', error);
        this.loading = false;
      }
    });
  }

  applyFilters() {
    console.log('Applying filters:', this.filterForm.value);
    // TODO: Implement filter logic
  }

  resetFilters() {
    this.filterForm.reset();
  }

  exportReport() {
    console.log('Exporting report...');
    // TODO: Implement export logic
  }

  // Chart helper methods
  getStatusCount(status: string): number {
    if (!this.bugMetrics?.bugsByStatus) return 0;
    return this.bugMetrics.bugsByStatus[status as keyof typeof this.bugMetrics.bugsByStatus] || 0;
  }

  getSeverityCount(severity: string): number {
    if (!this.bugMetrics?.bugsBySeverity) return 0;
    return this.bugMetrics.bugsBySeverity[severity as keyof typeof this.bugMetrics.bugsBySeverity] || 0;
  }

  getSeverityPercentage(severity: string): number {
    const count = this.getSeverityCount(severity);
    const total = this.bugMetrics?.totalBugs || 1;
    return (count / total) * 100;
  }

  getCircumference(): number {
    return 2 * Math.PI * 80; // radius = 80
  }

  getStatusOffset(status: string): number {
    const circumference = this.getCircumference();
    const percentage = this.getStatusCount(status) / (this.bugMetrics?.totalBugs || 1);
    return circumference - (percentage * circumference);
  }

  getMatrixValue(severity: string, priority: string): number {
    // Mock data for priority vs severity matrix
    const mockData: { [key: string]: { [key: string]: number } } = {
      'CRITICAL': { 'LOW': 1, 'MEDIUM': 3, 'HIGH': 8, 'CRITICAL': 12 },
      'HIGH': { 'LOW': 2, 'MEDIUM': 5, 'HIGH': 15, 'CRITICAL': 8 },
      'MEDIUM': { 'LOW': 8, 'MEDIUM': 12, 'HIGH': 10, 'CRITICAL': 3 },
      'LOW': { 'LOW': 15, 'MEDIUM': 8, 'HIGH': 4, 'CRITICAL': 1 }
    };
    return mockData[severity]?.[priority] || 0;
  }

  getMatrixColor(priority: number, severity: number): string {
    const intensity = (priority + severity) / 8;
    const red = Math.floor(255 * intensity);
    const green = Math.floor(255 * (1 - intensity));
    return `rgba(${red}, ${green}, 100, 0.7)`;
  }
}
