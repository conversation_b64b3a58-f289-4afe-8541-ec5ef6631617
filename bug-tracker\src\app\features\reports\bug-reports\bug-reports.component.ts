import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { BugService } from '../../../core/services/bug.service';
import { ProjectService } from '../../../core/services/project.service';
import { UserService } from '../../../core/services/user.service';
import { BugStatus, BugSeverity, BugPriority, BugMetrics } from '../../../core/models/bug.model';
import { Project } from '../../../core/models/project.model';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-bug-reports',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="reports-container">
      <div class="reports-header">
        <h1>Bug Reports & Analytics</h1>
        <p>Comprehensive bug tracking and analysis dashboard</p>
      </div>

      <!-- Filters Section -->
      <div class="filters-section">
        <form [formGroup]="filterForm" class="filters-form">
          <div class="filter-row">
            <div class="filter-group">
              <label>Date Range</label>
              <div class="date-range">
                <input type="date" formControlName="startDate" class="form-control">
                <span>to</span>
                <input type="date" formControlName="endDate" class="form-control">
              </div>
            </div>

            <div class="filter-group">
              <label>Status</label>
              <select formControlName="status" class="form-control">
                <option value="">All Statuses</option>
                <option value="NEW">New</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="RESOLVED">Resolved</option>
                <option value="CLOSED">Closed</option>
                <option value="REOPENED">Reopened</option>
              </select>
            </div>

            <div class="filter-group">
              <label>Project</label>
              <select formControlName="projectId" class="form-control">
                <option value="">All Projects</option>
                <option *ngFor="let project of projects" [value]="project.id">
                  {{ project.name }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>Assignee</label>
              <select formControlName="assigneeId" class="form-control">
                <option value="">All Assignees</option>
                <option *ngFor="let user of users" [value]="user.id">
                  {{ user.fullName }}
                </option>
              </select>
            </div>
          </div>

          <div class="filter-actions">
            <button type="button" (click)="applyFilters()" class="btn btn-primary">
              Apply Filters
            </button>
            <button type="button" (click)="resetFilters()" class="btn btn-outline">
              Reset
            </button>
            <button type="button" (click)="exportReport()" class="btn btn-success">
              Export Report
            </button>
          </div>
        </form>
      </div>

      <!-- Summary Cards -->
      <div class="summary-cards">
        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-bug"></i>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.totalBugs || 0 }}</h3>
            <p>Total Bugs</p>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.openBugs || 0 }}</h3>
            <p>Open Bugs</p>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.closedBugs || 0 }}</h3>
            <p>Closed Bugs</p>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-fire"></i>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.criticalBugs || 0 }}</h3>
            <p>Critical Bugs</p>
          </div>
        </div>

        <div class="summary-card">
          <div class="card-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="card-content">
            <h3>{{ bugMetrics?.averageTimeToFix || 0 }}d</h3>
            <p>Avg Resolution Time</p>
          </div>
        </div>
      </div>

      <!-- Charts Section (Placeholder) -->
      <div class="charts-section">
        <div class="chart-placeholder">
          <h3>Bug Analytics Charts</h3>
          <p>Charts will be implemented in the next phase</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .reports-container {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .reports-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .reports-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 0.5rem;
    }

    .reports-header p {
      font-size: 1.1rem;
      color: #6b7280;
    }

    .filters-section {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-group label {
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }

    .form-control {
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.875rem;
    }

    .date-range {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .date-range span {
      color: #6b7280;
      font-size: 0.875rem;
    }

    .filter-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
    }

    .btn-outline {
      background: transparent;
      color: #6b7280;
      border: 1px solid #d1d5db;
    }

    .btn-success {
      background: #10b981;
      color: white;
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #3b82f6;
      color: white;
      font-size: 1.5rem;
    }

    .card-content h3 {
      font-size: 2rem;
      font-weight: 700;
      color: #1f2937;
      margin: 0;
    }

    .card-content p {
      color: #6b7280;
      margin: 0;
      font-size: 0.875rem;
    }

    .charts-section {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .chart-placeholder {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }

    .chart-placeholder h3 {
      margin-bottom: 0.5rem;
      color: #374151;
    }
  `]
})
export class BugReportsComponent implements OnInit {
  private fb = inject(FormBuilder);
  private bugService = inject(BugService);
  private projectService = inject(ProjectService);
  private userService = inject(UserService);

  filterForm!: FormGroup;
  projects: Project[] = [];
  users: User[] = [];
  bugMetrics: BugMetrics | null = null;
  loading = false;

  ngOnInit() {
    this.initializeForm();
    this.loadData();
  }

  private initializeForm() {
    this.filterForm = this.fb.group({
      startDate: [''],
      endDate: [''],
      status: [''],
      projectId: [''],
      assigneeId: ['']
    });
  }

  private loadData() {
    this.loading = true;

    // Load projects
    this.projectService.projects$.subscribe({
      next: (projects) => {
        this.projects = projects;
      },
      error: (error) => {
        console.error('Error loading projects:', error);
      }
    });

    // Load users
    this.userService.getAllUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
      }
    });

    // Load bug metrics
    this.bugService.getBugMetrics().subscribe({
      next: (metrics) => {
        this.bugMetrics = metrics;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading bug metrics:', error);
        this.loading = false;
      }
    });
  }

  applyFilters() {
    console.log('Applying filters:', this.filterForm.value);
    // TODO: Implement filter logic
  }

  resetFilters() {
    this.filterForm.reset();
  }

  exportReport() {
    console.log('Exporting report...');
    // TODO: Implement export logic
  }
}
