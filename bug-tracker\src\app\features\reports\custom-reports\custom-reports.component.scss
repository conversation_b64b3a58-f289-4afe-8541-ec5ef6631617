.custom-reports-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--color-background);
  min-height: 100vh;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--color-border);

  .header-content {
    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin: 0 0 0.5rem 0;
    }

    .page-subtitle {
      font-size: 1rem;
      color: var(--color-text-secondary);
      margin: 0;
      max-width: 600px;
    }
  }

  .header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;

    .btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      white-space: nowrap;
    }
  }
}

.templates-panel {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  margin-bottom: 2rem;
  overflow: hidden;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--color-border);
    background: var(--color-background);

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin: 0;
    }

    .btn-close {
      background: none;
      border: none;
      font-size: 1.5rem;
      color: var(--color-text-secondary);
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 4px;

      &:hover {
        background: var(--color-border);
      }
    }
  }

  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
  }

  .template-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary-300);
      background: var(--color-primary-50);
    }

    .template-icon {
      width: 48px;
      height: 48px;
      background: var(--color-primary-500);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
    }

    .template-content {
      flex: 1;

      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--color-text-primary);
        margin: 0 0 0.25rem 0;
      }

      p {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        margin: 0 0 0.5rem 0;
      }

      .template-meta {
        display: flex;
        gap: 1rem;

        .category {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
          background: var(--color-primary-100);
          color: var(--color-primary-700);
          border-radius: 4px;
        }

        .usage {
          font-size: 0.75rem;
          color: var(--color-text-secondary);
        }
      }
    }
  }
}

.reports-content {
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;

    .search-filters {
      display: flex;
      gap: 1rem;
      flex: 1;

      .form-control {
        max-width: 250px;
      }
    }

    .view-options {
      display: flex;
      gap: 0.5rem;

      .btn {
        padding: 0.5rem;
        border: 1px solid var(--color-border);
        background: var(--color-surface);
        color: var(--color-text-secondary);

        &.active {
          background: var(--color-primary-500);
          color: white;
          border-color: var(--color-primary-500);
        }
      }
    }
  }

  .reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
  }

  .report-card {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary-200);
      transform: translateY(-2px);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 1.5rem;
      border-bottom: 1px solid var(--color-border);

      .report-info {
        flex: 1;

        h4 {
          font-size: 1.125rem;
          font-weight: 600;
          color: var(--color-text-primary);
          margin: 0 0 0.5rem 0;
        }

        p {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          margin: 0;
        }
      }

      .card-actions {
        display: flex;
        gap: 0.5rem;

        .btn-icon {
          width: 32px;
          height: 32px;
          border: none;
          background: var(--color-background);
          color: var(--color-text-secondary);
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: var(--color-primary-100);
            color: var(--color-primary-600);
          }

          &.danger:hover {
            background: var(--color-danger-100);
            color: var(--color-danger-600);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }

    .card-content {
      padding: 1.5rem;

      .report-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .category-badge {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-weight: 500;

          &.category-bug-analysis { background: var(--color-danger-100); color: var(--color-danger-700); }
          &.category-project-metrics { background: var(--color-primary-100); color: var(--color-primary-700); }
          &.category-team-performance { background: var(--color-success-100); color: var(--color-success-700); }
          &.category-productivity { background: var(--color-warning-100); color: var(--color-warning-700); }
          &.category-quality-assurance { background: var(--color-info-100); color: var(--color-info-700); }
          &.category-custom { background: var(--color-secondary-100); color: var(--color-secondary-700); }
        }

        .execution-count {
          font-size: 0.75rem;
          color: var(--color-text-secondary);
        }
      }

      .report-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;

        .stat {
          .stat-label {
            font-size: 0.75rem;
            color: var(--color-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block;
            margin-bottom: 0.25rem;
          }

          .stat-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-text-primary);
          }
        }
      }
    }

    .execution-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 1rem;

      .spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--color-border);
        border-top: 3px solid var(--color-primary-500);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      span {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        font-weight: 500;
      }
    }
  }

  .reports-table {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    overflow: hidden;

    .table {
      width: 100%;
      border-collapse: collapse;

      th, td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid var(--color-border);
      }

      th {
        background: var(--color-background);
        font-weight: 600;
        color: var(--color-text-primary);
        font-size: 0.875rem;
      }

      td {
        color: var(--color-text-secondary);
        font-size: 0.875rem;
      }

      .report-name {
        strong {
          color: var(--color-text-primary);
          display: block;
          margin-bottom: 0.25rem;
        }

        small {
          color: var(--color-text-secondary);
          font-size: 0.75rem;
        }
      }

      .table-actions {
        display: flex;
        gap: 0.5rem;

        .btn {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 768px) {
  .custom-reports-container {
    padding: 1rem;
  }

  .reports-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .header-actions {
      justify-content: flex-start;
    }
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .reports-grid {
    grid-template-columns: 1fr;
  }

  .list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;

    .search-filters {
      flex-direction: column;

      .form-control {
        max-width: none;
      }
    }
  }

  .report-card {
    .card-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;

      .card-actions {
        justify-content: center;
      }
    }

    .report-stats {
      grid-template-columns: 1fr;
    }
  }

  .reports-table {
    overflow-x: auto;

    .table {
      min-width: 600px;
    }
  }
}
