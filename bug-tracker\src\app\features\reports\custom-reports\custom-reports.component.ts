import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { CustomReportsService } from '../../../core/services/custom-reports.service';
import {
  CustomReport,
  ReportExecutionResult,
  ReportCategory,
  FilterOperator,
  ReportFilter,
  ReportDataSource
} from '../../../core/models/common.model';

@Component({
  selector: 'app-custom-reports',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  template: `
    <div class="custom-reports-container">
      <!-- Header -->
      <div class="reports-header">
        <div class="header-content">
          <h1 class="page-title">Custom Reports</h1>
          <p class="page-subtitle">Create, manage, and execute custom reports with flexible data sources and visualizations</p>
        </div>
        <div class="header-actions">
          <button class="btn btn-outline" (click)="showTemplates = !showTemplates">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="15" y2="9"></line>
              <line x1="9" y1="15" x2="15" y2="15"></line>
            </svg>
            Templates
          </button>
          <button class="btn btn-primary" (click)="createNewReport()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            New Report
          </button>
        </div>
      </div>

      <!-- Templates Panel -->
      <div *ngIf="showTemplates" class="templates-panel">
        <div class="panel-header">
          <h3>Report Templates</h3>
          <button class="btn-close" (click)="showTemplates = false">×</button>
        </div>
        <div class="templates-grid">
          <div *ngFor="let template of reportTemplates" class="template-card" (click)="useTemplate(template)">
            <div class="template-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <rect x="3" y="3" width="18" height="18" rx="2"></rect>
                <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" fill="none"></path>
              </svg>
            </div>
            <div class="template-content">
              <h4>{{ template.name }}</h4>
              <p>{{ template.description }}</p>
              <div class="template-meta">
                <span class="category">{{ template.category }}</span>
                <span class="usage">{{ template.executionCount }} uses</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="reports-content">
        <!-- Reports List -->
        <div *ngIf="currentView === 'list'" class="reports-list">
          <div class="list-header">
            <div class="search-filters">
              <input type="text" class="form-control" placeholder="Search reports..."
                     [(ngModel)]="searchTerm" (input)="filterReports()">
              <select class="form-control" [(ngModel)]="selectedCategory" (change)="filterReports()">
                <option value="">All Categories</option>
                <option *ngFor="let category of reportCategories" [value]="category">{{ category }}</option>
              </select>
            </div>
            <div class="view-options">
              <button class="btn btn-sm" [class.active]="viewMode === 'grid'" (click)="viewMode = 'grid'">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <rect x="3" y="3" width="7" height="7"></rect>
                  <rect x="14" y="3" width="7" height="7"></rect>
                  <rect x="14" y="14" width="7" height="7"></rect>
                  <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
              </button>
              <button class="btn btn-sm" [class.active]="viewMode === 'table'" (click)="viewMode = 'table'">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <rect x="3" y="5" width="18" height="2"></rect>
                  <rect x="3" y="11" width="18" height="2"></rect>
                  <rect x="3" y="17" width="18" height="2"></rect>
                </svg>
              </button>
            </div>
          </div>

          <!-- Grid View -->
          <div *ngIf="viewMode === 'grid'" class="reports-grid">
            <div *ngFor="let report of filteredReports" class="report-card">
              <div class="card-header">
                <div class="report-info">
                  <h4>{{ report.name }}</h4>
                  <p>{{ report.description }}</p>
                </div>
                <div class="card-actions">
                  <button class="btn-icon" (click)="executeReport(report)" [disabled]="executingReports.has(report.id)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <polygon points="5,3 19,12 5,21"></polygon>
                    </svg>
                  </button>
                  <button class="btn-icon" (click)="editReport(report)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                  </button>
                  <button class="btn-icon" (click)="duplicateReport(report.id)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </button>
                  <button class="btn-icon danger" (click)="deleteReport(report.id)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <polyline points="3,6 5,6 21,6"></polyline>
                      <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div class="card-content">
                <div class="report-meta">
                  <span class="category-badge" [class]="'category-' + getCategoryClass(report.category)">
                    {{ report.category }}
                  </span>
                  <span class="execution-count">{{ report.executionCount }} executions</span>
                </div>
                <div class="report-stats">
                  <div class="stat">
                    <span class="stat-label">Data Source</span>
                    <span class="stat-value">{{ getDataSourceLabel(report.configuration.dataSource.type) }}</span>
                  </div>
                  <div class="stat">
                    <span class="stat-label">Last Run</span>
                    <span class="stat-value">{{ formatDate(report.lastExecuted) }}</span>
                  </div>
                </div>
              </div>
              <div *ngIf="executingReports.has(report.id)" class="execution-overlay">
                <div class="spinner"></div>
                <span>Executing...</span>
              </div>
            </div>
          </div>

          <!-- Table View -->
          <div *ngIf="viewMode === 'table'" class="reports-table">
            <table class="table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Data Source</th>
                  <th>Executions</th>
                  <th>Last Run</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let report of filteredReports">
                  <td>
                    <div class="report-name">
                      <strong>{{ report.name }}</strong>
                      <small>{{ report.description }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="category-badge" [class]="'category-' + getCategoryClass(report.category)">
                      {{ report.category }}
                    </span>
                  </td>
                  <td>{{ getDataSourceLabel(report.configuration.dataSource.type) }}</td>
                  <td>{{ report.executionCount }}</td>
                  <td>{{ formatDate(report.lastExecuted) }}</td>
                  <td>
                    <div class="table-actions">
                      <button class="btn btn-sm btn-primary" (click)="executeReport(report)"
                              [disabled]="executingReports.has(report.id)">
                        Run
                      </button>
                      <button class="btn btn-sm btn-outline" (click)="editReport(report)">Edit</button>
                      <button class="btn btn-sm btn-outline" (click)="duplicateReport(report.id)">Copy</button>
                      <button class="btn btn-sm btn-danger" (click)="deleteReport(report.id)">Delete</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./custom-reports.component.scss']
})
export class CustomReportsComponent implements OnInit {
  private customReportsService = inject(CustomReportsService);
  private fb = inject(FormBuilder);

  reports: CustomReport[] = [];
  filteredReports: CustomReport[] = [];
  reportTemplates: CustomReport[] = [];

  currentView: 'list' | 'builder' | 'results' = 'list';
  viewMode: 'grid' | 'table' = 'grid';
  showTemplates = false;

  searchTerm = '';
  selectedCategory = '';
  reportCategories = Object.values(ReportCategory);

  executingReports = new Set<string>();
  currentExecution: ReportExecutionResult | null = null;

  // Report builder form
  reportForm!: FormGroup;
  editingReport: CustomReport | null = null;

  ngOnInit() {
    this.initializeForm();
    this.loadReports();
    this.loadTemplates();
  }

  private initializeForm() {
    this.reportForm = this.fb.group({
      name: ['', Validators.required],
      description: [''],
      category: [ReportCategory.CUSTOM, Validators.required],
      isPublic: [false],
      dataSource: this.fb.group({
        type: ['bugs', Validators.required],
        tables: this.fb.array(['bugs'])
      }),
      filters: this.fb.array([]),
      columns: this.fb.array([]),
      sorting: this.fb.array([]),
      visualization: this.fb.group({
        type: ['table'],
        chartType: ['bar'],
        layout: ['single'],
        options: this.fb.group({
          showLegend: [true],
          showGrid: [true],
          height: [400],
          responsive: [true]
        })
      })
    });
  }

  loadReports() {
    this.customReportsService.getAllReports().subscribe({
      next: (reports) => {
        this.reports = reports;
        this.filterReports();
      },
      error: (error) => {
        console.error('Error loading reports:', error);
      }
    });
  }

  loadTemplates() {
    this.customReportsService.getReportTemplates().subscribe({
      next: (templates) => {
        this.reportTemplates = templates;
      },
      error: (error) => {
        console.error('Error loading templates:', error);
      }
    });
  }

  filterReports() {
    this.filteredReports = this.reports.filter(report => {
      const matchesSearch = !this.searchTerm ||
        report.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        report.description.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory = !this.selectedCategory || report.category === this.selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }

  createNewReport() {
    this.editingReport = null;
    this.reportForm.reset();
    this.initializeForm();
    this.currentView = 'builder';
  }

  editReport(report: CustomReport) {
    this.editingReport = report;
    this.populateForm(report);
    this.currentView = 'builder';
  }

  private populateForm(report: CustomReport) {
    this.reportForm.patchValue({
      name: report.name,
      description: report.description,
      category: report.category,
      isPublic: report.isPublic,
      dataSource: report.configuration.dataSource,
      visualization: report.configuration.visualization
    });

    // Populate filters, columns, and sorting arrays
    // This would be more complex in a real implementation
  }

  useTemplate(template: CustomReport) {
    this.editingReport = null;
    this.populateForm(template);
    this.reportForm.patchValue({
      name: `${template.name} (Copy)`,
      isPublic: false
    });
    this.currentView = 'builder';
    this.showTemplates = false;
  }

  executeReport(report: CustomReport) {
    this.executingReports.add(report.id);

    this.customReportsService.executeReport(report.id).subscribe({
      next: (result) => {
        this.currentExecution = result;
        this.currentView = 'results';
        this.executingReports.delete(report.id);
      },
      error: (error) => {
        console.error('Error executing report:', error);
        this.executingReports.delete(report.id);
      }
    });
  }

  duplicateReport(reportId: string) {
    this.customReportsService.duplicateReport(reportId).subscribe({
      next: () => {
        this.loadReports();
      },
      error: (error) => {
        console.error('Error duplicating report:', error);
      }
    });
  }

  deleteReport(reportId: string) {
    if (confirm('Are you sure you want to delete this report?')) {
      this.customReportsService.deleteReport(reportId).subscribe({
        next: () => {
          this.loadReports();
        },
        error: (error) => {
          console.error('Error deleting report:', error);
        }
      });
    }
  }

  getCategoryClass(category: ReportCategory): string {
    return category.toLowerCase().replace(/\s+/g, '-');
  }

  getDataSourceLabel(type: string): string {
    const labels: { [key: string]: string } = {
      'bugs': 'Bugs',
      'projects': 'Projects',
      'users': 'Users',
      'team_performance': 'Team Performance',
      'custom_query': 'Custom Query'
    };
    return labels[type] || type;
  }

  formatDate(date: Date | undefined): string {
    if (!date) return 'Never';
    return new Date(date).toLocaleDateString();
  }
}
