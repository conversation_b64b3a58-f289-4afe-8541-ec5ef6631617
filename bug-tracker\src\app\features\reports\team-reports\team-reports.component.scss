.team-reports-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--color-background);
  min-height: 100vh;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--color-border);

  .header-content {
    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin: 0 0 0.5rem 0;
    }

    .page-subtitle {
      font-size: 1rem;
      color: var(--color-text-secondary);
      margin: 0;
      max-width: 600px;
    }
  }

  .header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;

    .form-control {
      min-width: 150px;
    }

    .btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      white-space: nowrap;
    }
  }
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-border);
    border-top: 4px solid var(--color-primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  h3 {
    color: var(--color-text-primary);
    margin-bottom: 0.5rem;
  }

  p {
    color: var(--color-text-secondary);
    margin-bottom: 1.5rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.overview-section {
  margin-bottom: 3rem;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 1.5rem;
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .overview-card {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary-200);
      transform: translateY(-2px);
    }

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;

      &.team-members { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.productivity { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
      &.bugs-resolved { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
      &.efficiency { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    }

    .card-content {
      flex: 1;

      h3 {
        font-size: 2rem;
        font-weight: 700;
        color: var(--color-text-primary);
        margin: 0 0 0.25rem 0;
      }

      p {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        margin: 0 0 0.5rem 0;
        font-weight: 500;
      }

      .card-detail {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 500;

        &.excellent { background: var(--color-success-100); color: var(--color-success-700); }
        &.good { background: var(--color-primary-100); color: var(--color-primary-700); }
        &.average { background: var(--color-warning-100); color: var(--color-warning-700); }
        &.poor { background: var(--color-danger-100); color: var(--color-danger-700); }
        &.risk-low { background: var(--color-success-100); color: var(--color-success-700); }
        &.risk-medium { background: var(--color-warning-100); color: var(--color-warning-700); }
        &.risk-high { background: var(--color-danger-100); color: var(--color-danger-700); }
      }
    }
  }
}

.performance-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin: 0;
    }

    .section-filters {
      display: flex;
      gap: 1rem;

      .form-control-sm {
        min-width: 140px;
      }
    }
  }

  .performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
  }

  .performance-card {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary-200);
      transform: translateY(-2px);
    }

    .member-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--color-border);

      .member-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: var(--color-primary-500);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1.125rem;
        }
      }

      .member-info {
        flex: 1;

        .member-name {
          font-size: 1.125rem;
          font-weight: 600;
          color: var(--color-text-primary);
          margin: 0 0 0.25rem 0;
        }

        .member-role {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          margin: 0 0 0.5rem 0;
        }

        .performance-badge {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
          border-radius: 6px;
          font-weight: 500;

          &.badge-excellent { background: var(--color-success-100); color: var(--color-success-700); }
          &.badge-good { background: var(--color-primary-100); color: var(--color-primary-700); }
          &.badge-average { background: var(--color-warning-100); color: var(--color-warning-700); }
          &.badge-needs_improvement { background: var(--color-danger-100); color: var(--color-danger-700); }
        }
      }
    }

    .member-metrics {
      .metric-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1.5rem;

        .metric {
          text-align: center;

          .metric-value {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-text-primary);
          }

          .metric-label {
            font-size: 0.75rem;
            color: var(--color-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }

      .performance-scores {
        margin-bottom: 1.5rem;

        .score-item {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-bottom: 0.75rem;

          .score-label {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
            min-width: 80px;
          }

          .score-bar {
            flex: 1;
            height: 8px;
            background: var(--color-border);
            border-radius: 4px;
            overflow: hidden;

            .score-fill {
              height: 100%;
              background: var(--color-primary-500);
              border-radius: 4px;
              transition: width 0.3s ease;

              &.quality { background: var(--color-success-500); }
              &.workload { background: var(--color-warning-500); }
            }
          }

          .score-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-text-primary);
            min-width: 40px;
            text-align: right;
          }
        }
      }

      .member-details {
        .strengths, .improvements {
          margin-bottom: 1rem;

          h5 {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-text-primary);
            margin: 0 0 0.5rem 0;
          }

          .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;

            .tag {
              font-size: 0.75rem;
              padding: 0.25rem 0.5rem;
              border-radius: 6px;
              font-weight: 500;

              &.tag-success { background: var(--color-success-100); color: var(--color-success-700); }
              &.tag-warning { background: var(--color-warning-100); color: var(--color-warning-700); }
            }
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .team-reports-container {
    padding: 1rem;
  }

  .reports-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .header-actions {
      justify-content: flex-start;
    }
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .performance-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .section-filters {
      justify-content: flex-start;
    }
  }

  .performance-card {
    padding: 1rem;

    .member-header {
      flex-direction: column;
      text-align: center;
      gap: 0.75rem;
    }

    .metric-row {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .productivity-grid {
    grid-template-columns: 1fr;
  }

  .workload-grid {
    grid-template-columns: 1fr;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }
}

// Team Productivity Section
.productivity-section {
  margin-bottom: 3rem;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 1.5rem;
  }

  .productivity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .productivity-card {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;

    .card-header {
      margin-bottom: 1rem;

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--color-text-primary);
        margin-bottom: 1rem;
      }

      .velocity-chart {
        height: 80px;
        display: flex;
        align-items: end;
        justify-content: center;

        .velocity-bar {
          width: 40px;
          background: linear-gradient(to top, var(--color-primary-500), var(--color-primary-300));
          border-radius: 4px 4px 0 0;
          position: relative;
          min-height: 20px;

          .velocity-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 600;
            color: var(--color-text-primary);
          }
        }
      }

      .utilization-circle {
        position: relative;
        display: inline-block;

        .utilization-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .utilization-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--color-text-primary);
          }
        }
      }

      .review-metrics {
        display: flex;
        justify-content: space-around;
        margin-bottom: 1rem;

        .review-metric {
          text-align: center;

          .metric-value {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-primary-500);
          }

          .metric-label {
            font-size: 0.75rem;
            color: var(--color-text-secondary);
          }
        }
      }

      .collaboration-gauge {
        position: relative;
        height: 20px;
        background: var(--color-border);
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 0.5rem;

        .gauge-fill {
          height: 100%;
          background: linear-gradient(to right, var(--color-success-500), var(--color-primary-500));
          border-radius: 10px;
          transition: width 0.3s ease;
        }

        .gauge-value {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 0.875rem;
          font-weight: 600;
          color: white;
          text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
      }
    }

    .card-content {
      p {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        margin: 0.25rem 0;

        &.trend {
          font-weight: 600;
          padding: 0.5rem;
          border-radius: 6px;

          &.trend-increasing { background: var(--color-success-100); color: var(--color-success-700); }
          &.trend-stable { background: var(--color-primary-100); color: var(--color-primary-700); }
          &.trend-decreasing { background: var(--color-danger-100); color: var(--color-danger-700); }
        }
      }

      .quality-score {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--color-text-primary);
        padding: 0.5rem;
        background: var(--color-success-100);
        color: var(--color-success-700);
        border-radius: 6px;
      }
    }
  }
}

// Workload Distribution Section
.workload-section {
  margin-bottom: 3rem;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin: 0;
    }

    .workload-status {
      .status-badge {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 600;

        &.status-balanced { background: var(--color-success-100); color: var(--color-success-700); }
        &.status-unbalanced { background: var(--color-warning-100); color: var(--color-warning-700); }
        &.status-critical { background: var(--color-danger-100); color: var(--color-danger-700); }
      }
    }
  }

  .workload-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .workload-card {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;

    .workload-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h4 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--color-text-primary);
        margin: 0;
      }

      .workload-status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 500;

        &.status-overloaded { background: var(--color-danger-100); color: var(--color-danger-700); }
        &.status-optimal { background: var(--color-success-100); color: var(--color-success-700); }
        &.status-underutilized { background: var(--color-warning-100); color: var(--color-warning-700); }
      }
    }

    .workload-progress {
      margin-bottom: 1.5rem;

      .progress-bar {
        height: 12px;
        background: var(--color-border);
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 0.5rem;

        .progress-fill {
          height: 100%;
          border-radius: 6px;
          transition: width 0.3s ease;

          &.low { background: var(--color-warning-500); }
          &.normal { background: var(--color-success-500); }
          &.high { background: var(--color-warning-600); }
          &.overloaded { background: var(--color-danger-500); }
        }
      }

      .progress-text {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        font-weight: 500;
      }
    }

    .workload-details {
      .workload-breakdown {
        margin-bottom: 1rem;

        .breakdown-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 0.5rem;

          .item-icon {
            font-size: 1rem;
          }

          .item-label {
            flex: 1;
            font-size: 0.875rem;
            color: var(--color-text-secondary);
          }

          .item-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-text-primary);
          }
        }
      }

      .capacity-info {
        padding-top: 1rem;
        border-top: 1px solid var(--color-border);

        .capacity-text {
          display: block;
          font-size: 0.875rem;
          color: var(--color-text-primary);
          font-weight: 500;
          margin-bottom: 0.25rem;
        }

        .deadlines-text {
          font-size: 0.75rem;
          color: var(--color-warning-600);
          font-weight: 500;
        }
      }
    }
  }

  .recommendations-section {
    .recommendations-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 1rem;
    }

    .recommendations-list {
      display: grid;
      gap: 1rem;
    }

    .recommendation-card {
      background: var(--color-surface);
      border: 1px solid var(--color-border);
      border-radius: 8px;
      padding: 1rem;
      border-left: 4px solid var(--color-primary-500);

      &.priority-high { border-left-color: var(--color-danger-500); }
      &.priority-medium { border-left-color: var(--color-warning-500); }
      &.priority-low { border-left-color: var(--color-success-500); }

      .recommendation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .recommendation-type {
          font-weight: 600;
          color: var(--color-text-primary);
        }

        .recommendation-priority {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          background: var(--color-primary-100);
          color: var(--color-primary-700);
        }
      }

      .recommendation-text {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        margin-bottom: 0.5rem;
      }

      .recommendation-action {
        .task-count {
          font-size: 0.875rem;
          font-weight: 600;
          color: var(--color-primary-600);
        }
      }
    }
  }
}

// Performance Comparison Section
.comparison-section {
  margin-bottom: 3rem;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 1.5rem;
  }

  .comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
  }

  .comparison-card {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 1.5rem;
    }

    .comparison-metrics {
      .comparison-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: var(--color-background);
        border-radius: 8px;

        .metric-label {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          font-weight: 500;
        }

        .metric-comparison {
          display: flex;
          align-items: center;
          gap: 0.75rem;

          .current-value {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--color-text-primary);
          }

          .change-indicator {
            font-size: 0.875rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;

            &.positive { background: var(--color-success-100); color: var(--color-success-700); }
            &.negative { background: var(--color-danger-100); color: var(--color-danger-700); }
            &.neutral { background: var(--color-border); color: var(--color-text-secondary); }
          }
        }
      }
    }

    .benchmark-metrics {
      .benchmark-item {
        margin-bottom: 1rem;

        .benchmark-label {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          font-weight: 500;
          margin-bottom: 0.5rem;
          display: block;
        }

        .benchmark-bar {
          position: relative;
          height: 24px;
          background: var(--color-border);
          border-radius: 12px;
          overflow: hidden;

          .benchmark-fill {
            height: 100%;
            border-radius: 12px;
            transition: width 0.3s ease;

            &.industry { background: var(--color-primary-500); }
            &.company { background: var(--color-success-500); }
          }

          .benchmark-value {
            position: absolute;
            top: 50%;
            right: 8px;
            transform: translateY(-50%);
            font-size: 0.875rem;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
          }
        }

        .ranking-display {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem;
          background: var(--color-background);
          border-radius: 8px;

          .ranking-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-primary-500);
          }

          .ranking-label {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
          }
        }
      }
    }
  }
}
