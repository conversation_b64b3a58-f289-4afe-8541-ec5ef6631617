import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TeamPerformanceService } from '../../../core/services/team-performance.service';
import {
  TeamPerformanceMetrics,
  IndividualPerformanceMetrics,
  MemberWorkload,
  PerformanceTrendData
} from '../../../core/models/common.model';

@Component({
  selector: 'app-team-reports',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="team-reports-container">
      <!-- Header -->
      <div class="reports-header">
        <div class="header-content">
          <h1 class="page-title">Team Performance Reports</h1>
          <p class="page-subtitle">Comprehensive analysis of team productivity, individual performance, and workload distribution</p>
        </div>
        <div class="header-actions">
          <select class="form-control" [(ngModel)]="selectedPeriod" (change)="onPeriodChange()">
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
            <option value="180">Last 6 months</option>
          </select>
          <button class="btn btn-primary" (click)="refreshData()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
              <path d="M21 3v5h-5"></path>
              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
              <path d="M3 21v-5h5"></path>
            </svg>
            Refresh
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>Loading team performance data...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="errorMessage" class="error-container">
        <div class="error-icon">⚠️</div>
        <h3>Error Loading Data</h3>
        <p>{{ errorMessage }}</p>
        <button class="btn btn-primary" (click)="refreshData()">Try Again</button>
      </div>

      <!-- Main Content -->
      <div *ngIf="!loading && !errorMessage && performanceData" class="reports-content">

        <!-- Team Overview Cards -->
        <div class="overview-section">
          <h2 class="section-title">Team Overview</h2>
          <div class="overview-grid">
            <div class="overview-card">
              <div class="card-icon team-members">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z"></path>
                  <path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z"></path>
                </svg>
              </div>
              <div class="card-content">
                <h3>{{ performanceData.teamOverview.totalMembers }}</h3>
                <p>Total Members</p>
                <span class="card-detail">{{ performanceData.teamOverview.activeMembers }} active</span>
              </div>
            </div>

            <div class="overview-card">
              <div class="card-icon productivity">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L2 7V17C2 18.1 2.9 19 4 19H20C21.1 19 22 18.1 22 17V7L12 2Z"></path>
                  <path d="M12 22V12"></path>
                </svg>
              </div>
              <div class="card-content">
                <h3>{{ performanceData.teamOverview.averageProductivity }}%</h3>
                <p>Avg Productivity</p>
                <span class="card-detail" [class]="getProductivityClass(performanceData.teamOverview.averageProductivity)">
                  {{ getProductivityLabel(performanceData.teamOverview.averageProductivity) }}
                </span>
              </div>
            </div>

            <div class="overview-card">
              <div class="card-icon bugs-resolved">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 12L11 14L15 10"></path>
                  <circle cx="12" cy="12" r="10"></circle>
                </svg>
              </div>
              <div class="card-content">
                <h3>{{ performanceData.teamOverview.totalBugsResolved }}</h3>
                <p>Bugs Resolved</p>
                <span class="card-detail">{{ performanceData.teamOverview.averageResolutionTime }}h avg time</span>
              </div>
            </div>

            <div class="overview-card">
              <div class="card-icon efficiency">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z"></path>
                </svg>
              </div>
              <div class="card-content">
                <h3>{{ performanceData.teamOverview.teamEfficiencyScore }}%</h3>
                <p>Team Efficiency</p>
                <span class="card-detail" [class]="getBurnoutRiskClass(performanceData.teamOverview.teamBurnoutRisk)">
                  {{ performanceData.teamOverview.teamBurnoutRisk }} burnout risk
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Individual Performance Section -->
        <div class="performance-section">
          <div class="section-header">
            <h2 class="section-title">Individual Performance</h2>
            <div class="section-filters">
              <select class="form-control form-control-sm" [(ngModel)]="selectedRole" (change)="filterByRole()">
                <option value="">All Roles</option>
                <option value="Developer">Developers</option>
                <option value="QA Engineer">QA Engineers</option>
                <option value="Project Manager">Project Managers</option>
              </select>
              <select class="form-control form-control-sm" [(ngModel)]="selectedPerformance" (change)="filterByPerformance()">
                <option value="">All Performance</option>
                <option value="excellent">Excellent</option>
                <option value="good">Good</option>
                <option value="average">Average</option>
                <option value="needs_improvement">Needs Improvement</option>
              </select>
            </div>
          </div>

          <div class="performance-grid">
            <div *ngFor="let member of getFilteredMembers()" class="performance-card">
              <div class="member-header">
                <div class="member-avatar">
                  <img *ngIf="member.avatar" [src]="member.avatar" [alt]="member.userName">
                  <div *ngIf="!member.avatar" class="avatar-placeholder">
                    {{ getInitials(member.userName) }}
                  </div>
                </div>
                <div class="member-info">
                  <h4 class="member-name">{{ member.userName }}</h4>
                  <p class="member-role">{{ member.userRole }}</p>
                  <span class="performance-badge" [class]="'badge-' + member.performanceRating">
                    {{ getPerformanceLabel(member.performanceRating) }}
                  </span>
                </div>
              </div>

              <div class="member-metrics">
                <div class="metric-row">
                  <div class="metric">
                    <span class="metric-value">{{ member.performance.bugsAssigned }}</span>
                    <span class="metric-label">Assigned</span>
                  </div>
                  <div class="metric">
                    <span class="metric-value">{{ member.performance.bugsResolved }}</span>
                    <span class="metric-label">Resolved</span>
                  </div>
                  <div class="metric">
                    <span class="metric-value">{{ member.performance.fixRate }}%</span>
                    <span class="metric-label">Fix Rate</span>
                  </div>
                </div>

                <div class="performance-scores">
                  <div class="score-item">
                    <span class="score-label">Productivity</span>
                    <div class="score-bar">
                      <div class="score-fill" [style.width.%]="member.performance.productivityScore"></div>
                    </div>
                    <span class="score-value">{{ member.performance.productivityScore }}%</span>
                  </div>
                  <div class="score-item">
                    <span class="score-label">Quality</span>
                    <div class="score-bar">
                      <div class="score-fill quality" [style.width.%]="member.performance.qualityScore"></div>
                    </div>
                    <span class="score-value">{{ member.performance.qualityScore }}%</span>
                  </div>
                  <div class="score-item">
                    <span class="score-label">Workload</span>
                    <div class="score-bar">
                      <div class="score-fill workload" [style.width.%]="member.performance.workloadScore"></div>
                    </div>
                    <span class="score-value">{{ member.performance.workloadScore }}%</span>
                  </div>
                </div>

                <div class="member-details">
                  <div class="strengths">
                    <h5>Strengths</h5>
                    <div class="tags">
                      <span *ngFor="let strength of member.strengths" class="tag tag-success">{{ strength }}</span>
                    </div>
                  </div>
                  <div class="improvements" *ngIf="member.improvementAreas.length > 0">
                    <h5>Areas for Improvement</h5>
                    <div class="tags">
                      <span *ngFor="let area of member.improvementAreas" class="tag tag-warning">{{ area }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Team Productivity Section -->
        <div class="productivity-section">
          <h2 class="section-title">Team Productivity Metrics</h2>
          <div class="productivity-grid">
            <div class="productivity-card">
              <div class="card-header">
                <h3>Sprint Velocity</h3>
                <div class="velocity-chart">
                  <div class="velocity-bar" [style.height.%]="(performanceData.teamProductivity.sprintVelocity / 50) * 100">
                    <span class="velocity-value">{{ performanceData.teamProductivity.sprintVelocity }}</span>
                  </div>
                </div>
              </div>
              <div class="card-content">
                <p class="trend" [class]="'trend-' + performanceData.teamProductivity.productivityTrend">
                  {{ getProductivityTrendLabel(performanceData.teamProductivity.productivityTrend) }}
                </p>
              </div>
            </div>

            <div class="productivity-card">
              <div class="card-header">
                <h3>Team Utilization</h3>
                <div class="utilization-circle">
                  <svg width="80" height="80" viewBox="0 0 80 80">
                    <circle cx="40" cy="40" r="35" fill="none" stroke="var(--color-border)" stroke-width="8"></circle>
                    <circle cx="40" cy="40" r="35" fill="none" stroke="var(--color-primary-500)" stroke-width="8"
                            [attr.stroke-dasharray]="getCircumference()"
                            [attr.stroke-dashoffset]="getUtilizationOffset(performanceData.teamProductivity.utilizationRate)"
                            transform="rotate(-90 40 40)"></circle>
                  </svg>
                  <div class="utilization-text">
                    <span class="utilization-value">{{ performanceData.teamProductivity.utilizationRate }}%</span>
                  </div>
                </div>
              </div>
              <div class="card-content">
                <p>{{ performanceData.teamProductivity.teamCapacity }}h total capacity</p>
              </div>
            </div>

            <div class="productivity-card">
              <div class="card-header">
                <h3>Code Review Quality</h3>
                <div class="review-metrics">
                  <div class="review-metric">
                    <span class="metric-value">{{ performanceData.teamProductivity.codeReviewMetrics.averageReviewTime }}h</span>
                    <span class="metric-label">Avg Review Time</span>
                  </div>
                  <div class="review-metric">
                    <span class="metric-value">{{ performanceData.teamProductivity.codeReviewMetrics.reviewParticipation }}%</span>
                    <span class="metric-label">Participation</span>
                  </div>
                </div>
              </div>
              <div class="card-content">
                <div class="quality-score">
                  Quality Score: {{ performanceData.teamProductivity.codeReviewMetrics.qualityScore }}%
                </div>
              </div>
            </div>

            <div class="productivity-card">
              <div class="card-header">
                <h3>Collaboration Score</h3>
                <div class="collaboration-gauge">
                  <div class="gauge-fill" [style.width.%]="performanceData.teamProductivity.collaborationScore"></div>
                  <span class="gauge-value">{{ performanceData.teamProductivity.collaborationScore }}%</span>
                </div>
              </div>
              <div class="card-content">
                <p>{{ performanceData.teamProductivity.totalTasksCompleted }} tasks completed</p>
                <p>{{ performanceData.teamProductivity.averageTasksPerMember }} avg per member</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Workload Distribution Section -->
        <div class="workload-section">
          <div class="section-header">
            <h2 class="section-title">Workload Distribution</h2>
            <div class="workload-status">
              <span class="status-badge" [class]="'status-' + performanceData.workloadDistribution.workloadBalance">
                {{ getWorkloadBalanceLabel(performanceData.workloadDistribution.workloadBalance) }}
              </span>
            </div>
          </div>

          <div class="workload-grid">
            <div *ngFor="let workload of performanceData.workloadDistribution.memberWorkloads" class="workload-card">
              <div class="workload-header">
                <h4>{{ workload.userName }}</h4>
                <span class="workload-status-badge" [class]="'status-' + workload.workloadStatus">
                  {{ getWorkloadStatusLabel(workload.workloadStatus) }}
                </span>
              </div>

              <div class="workload-progress">
                <div class="progress-bar">
                  <div class="progress-fill"
                       [style.width.%]="Math.min(workload.utilizationPercentage, 100)"
                       [class]="getWorkloadProgressClass(workload.utilizationPercentage)"></div>
                </div>
                <span class="progress-text">{{ workload.utilizationPercentage }}% utilized</span>
              </div>

              <div class="workload-details">
                <div class="workload-breakdown">
                  <div class="breakdown-item">
                    <span class="item-icon">🐛</span>
                    <span class="item-label">Bugs</span>
                    <span class="item-value">{{ workload.taskBreakdown.bugs }}</span>
                  </div>
                  <div class="breakdown-item">
                    <span class="item-icon">⚡</span>
                    <span class="item-label">Features</span>
                    <span class="item-value">{{ workload.taskBreakdown.features }}</span>
                  </div>
                  <div class="breakdown-item">
                    <span class="item-icon">👁️</span>
                    <span class="item-label">Reviews</span>
                    <span class="item-value">{{ workload.taskBreakdown.reviews }}</span>
                  </div>
                  <div class="breakdown-item">
                    <span class="item-icon">📅</span>
                    <span class="item-label">Meetings</span>
                    <span class="item-value">{{ workload.taskBreakdown.meetings }}</span>
                  </div>
                </div>

                <div class="capacity-info">
                  <span class="capacity-text">{{ workload.currentWorkload }}h / {{ workload.capacity }}h capacity</span>
                  <span class="deadlines-text" *ngIf="workload.upcomingDeadlines > 0">
                    {{ workload.upcomingDeadlines }} upcoming deadlines
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Workload Recommendations -->
          <div class="recommendations-section" *ngIf="performanceData.workloadDistribution.recommendedRebalancing.length > 0">
            <h3 class="recommendations-title">Workload Rebalancing Recommendations</h3>
            <div class="recommendations-list">
              <div *ngFor="let recommendation of performanceData.workloadDistribution.recommendedRebalancing"
                   class="recommendation-card" [class]="'priority-' + recommendation.priority">
                <div class="recommendation-header">
                  <span class="recommendation-type">{{ getRecommendationType(recommendation.type) }}</span>
                  <span class="recommendation-priority">{{ recommendation.priority }} priority</span>
                </div>
                <p class="recommendation-text">{{ recommendation.reason }}</p>
                <div class="recommendation-action">
                  <span class="task-count">{{ recommendation.taskCount }} tasks</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Comparison Section -->
        <div class="comparison-section">
          <h2 class="section-title">Performance Comparison</h2>
          <div class="comparison-grid">
            <div class="comparison-card">
              <h3>Current vs Previous Period</h3>
              <div class="comparison-metrics">
                <div class="comparison-metric">
                  <span class="metric-label">Productivity</span>
                  <div class="metric-comparison">
                    <span class="current-value">{{ performanceData.teamComparison.currentPeriod.productivityScore }}%</span>
                    <span class="change-indicator" [class]="getChangeClass(performanceData.teamComparison.percentageChanges.productivity)">
                      {{ getChangeSymbol(performanceData.teamComparison.percentageChanges.productivity) }}{{ Math.abs(performanceData.teamComparison.percentageChanges.productivity) }}%
                    </span>
                  </div>
                </div>
                <div class="comparison-metric">
                  <span class="metric-label">Quality</span>
                  <div class="metric-comparison">
                    <span class="current-value">{{ performanceData.teamComparison.currentPeriod.qualityScore }}%</span>
                    <span class="change-indicator" [class]="getChangeClass(performanceData.teamComparison.percentageChanges.quality)">
                      {{ getChangeSymbol(performanceData.teamComparison.percentageChanges.quality) }}{{ Math.abs(performanceData.teamComparison.percentageChanges.quality) }}%
                    </span>
                  </div>
                </div>
                <div class="comparison-metric">
                  <span class="metric-label">Efficiency</span>
                  <div class="metric-comparison">
                    <span class="current-value">{{ performanceData.teamComparison.currentPeriod.averageResolutionTime }}h</span>
                    <span class="change-indicator" [class]="getChangeClass(performanceData.teamComparison.percentageChanges.efficiency)">
                      {{ getChangeSymbol(performanceData.teamComparison.percentageChanges.efficiency) }}{{ Math.abs(performanceData.teamComparison.percentageChanges.efficiency) }}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="comparison-card">
              <h3>Benchmark Comparison</h3>
              <div class="benchmark-metrics">
                <div class="benchmark-item">
                  <span class="benchmark-label">Industry Average</span>
                  <div class="benchmark-bar">
                    <div class="benchmark-fill industry" [style.width.%]="performanceData.teamComparison.benchmarkComparison.industryAverage"></div>
                    <span class="benchmark-value">{{ performanceData.teamComparison.benchmarkComparison.industryAverage }}%</span>
                  </div>
                </div>
                <div class="benchmark-item">
                  <span class="benchmark-label">Company Average</span>
                  <div class="benchmark-bar">
                    <div class="benchmark-fill company" [style.width.%]="performanceData.teamComparison.benchmarkComparison.companyAverage"></div>
                    <span class="benchmark-value">{{ performanceData.teamComparison.benchmarkComparison.companyAverage }}%</span>
                  </div>
                </div>
                <div class="benchmark-item">
                  <span class="benchmark-label">Team Ranking</span>
                  <div class="ranking-display">
                    <span class="ranking-value">#{{ performanceData.teamComparison.benchmarkComparison.teamRanking }}</span>
                    <span class="ranking-label">out of 10 teams</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./team-reports.component.scss']
})
export class TeamReportsComponent implements OnInit {
  private teamPerformanceService = inject(TeamPerformanceService);

  performanceData: TeamPerformanceMetrics | null = null;
  loading = false;
  errorMessage = '';

  selectedPeriod = '30';
  selectedRole = '';
  selectedPerformance = '';

  ngOnInit() {
    this.loadPerformanceData();
  }

  loadPerformanceData() {
    this.loading = true;
    this.errorMessage = '';

    this.teamPerformanceService.getTeamPerformanceMetrics().subscribe({
      next: (data) => {
        this.performanceData = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading team performance data:', error);
        this.errorMessage = 'Failed to load team performance data. Please try again.';
        this.loading = false;
      }
    });
  }

  refreshData() {
    this.loadPerformanceData();
  }

  onPeriodChange() {
    this.loadPerformanceData();
  }

  filterByRole() {
    // Filter logic will be implemented
  }

  filterByPerformance() {
    // Filter logic will be implemented
  }

  getFilteredMembers(): IndividualPerformanceMetrics[] {
    if (!this.performanceData) return [];

    let filtered = this.performanceData.individualPerformance;

    if (this.selectedRole) {
      filtered = filtered.filter(member => member.userRole === this.selectedRole);
    }

    if (this.selectedPerformance) {
      filtered = filtered.filter(member => member.performanceRating === this.selectedPerformance);
    }

    return filtered;
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  getProductivityClass(productivity: number): string {
    if (productivity >= 90) return 'excellent';
    if (productivity >= 80) return 'good';
    if (productivity >= 70) return 'average';
    return 'poor';
  }

  getProductivityLabel(productivity: number): string {
    if (productivity >= 90) return 'Excellent';
    if (productivity >= 80) return 'Good';
    if (productivity >= 70) return 'Average';
    return 'Needs Improvement';
  }

  getBurnoutRiskClass(risk: string): string {
    return `risk-${risk}`;
  }

  getPerformanceLabel(rating: string): string {
    const labels: { [key: string]: string } = {
      'excellent': 'Excellent',
      'good': 'Good',
      'average': 'Average',
      'needs_improvement': 'Needs Improvement'
    };
    return labels[rating] || rating;
  }

  getProductivityTrendLabel(trend: string): string {
    const labels: { [key: string]: string } = {
      'increasing': '↗️ Increasing',
      'stable': '➡️ Stable',
      'decreasing': '↘️ Decreasing'
    };
    return labels[trend] || trend;
  }

  getCircumference(): number {
    return 2 * Math.PI * 35; // radius = 35
  }

  getUtilizationOffset(percentage: number): number {
    const circumference = this.getCircumference();
    return circumference - (percentage / 100) * circumference;
  }

  getWorkloadBalanceLabel(balance: string): string {
    const labels: { [key: string]: string } = {
      'balanced': 'Well Balanced',
      'unbalanced': 'Needs Attention',
      'critical': 'Critical Imbalance'
    };
    return labels[balance] || balance;
  }

  getWorkloadStatusLabel(status: string): string {
    const labels: { [key: string]: string } = {
      'overloaded': 'Overloaded',
      'optimal': 'Optimal',
      'underutilized': 'Underutilized'
    };
    return labels[status] || status;
  }

  getWorkloadProgressClass(percentage: number): string {
    if (percentage > 100) return 'overloaded';
    if (percentage > 90) return 'high';
    if (percentage < 60) return 'low';
    return 'normal';
  }

  getRecommendationType(type: string): string {
    const types: { [key: string]: string } = {
      'redistribute': 'Redistribute Tasks',
      'reduce': 'Reduce Workload',
      'increase': 'Increase Workload'
    };
    return types[type] || type;
  }

  getChangeClass(change: number): string {
    if (change > 0) return 'positive';
    if (change < 0) return 'negative';
    return 'neutral';
  }

  getChangeSymbol(change: number): string {
    if (change > 0) return '+';
    if (change < 0) return '';
    return '';
  }

  // Add Math to component for template access
  Math = Math;
}
